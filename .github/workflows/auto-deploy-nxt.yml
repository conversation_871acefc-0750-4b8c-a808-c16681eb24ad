name: "🤖 | Deploy | Nextracker"

on:
  push:
    branches:
      - "main"

env:
  MODULE_ID: sample_flask

jobs:
  deploy:
    # if this workflow is running from a NXT repo
    if: ${{ !startsWith(github.repository, 'sensehawk/') }}
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Set module name
        id: module-id
        run: |
          echo "module-id=${{ env.MODULE_ID }}" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get short SHA
        id: get_sha
        run: |
          echo "Full SHA: ${GITHUB_SHA}"
          SHORT_SHA=$(echo ${GITHUB_SHA} | cut -c1-7)
          echo "Short SHA: ${SHORT_SHA}"
          echo "sha7=${SHORT_SHA}" >> $GITHUB_OUTPUT

      - name: Determine Docker image name
        id: image-name
        run: |
          MODULE=${{ steps.module-id.outputs.module-id }}
          IMAGE_NAME="nextracker/nxt_sh_${{ env.MODULE_ID }}"
          echo "name=${IMAGE_NAME}" >> $GITHUB_OUTPUT

      # SIMULATED: Login to DockerHub
      - name: Login to DockerHub (Simulated)
        run: |
          echo "🐳 Simulating DockerHub login..."
          echo "✅ Successfully logged in to DockerHub (simulated)"

      # SIMULATED: Set up Docker Buildx
      - name: Set up Docker Buildx (Simulated)
        run: |
          echo "🔧 Simulating Docker Buildx setup..."
          echo "✅ Docker Buildx configured (simulated)"

      # SIMULATED: Build and push Docker image
      - name: Build and push Docker image (Simulated)
        id: docker_build
        run: |
          echo "🏗️ Simulating Docker image build and push..."
          echo "Building image: ${{ steps.image-name.outputs.name }}:${{ steps.get_sha.outputs.sha7 }}"
          echo "Context: ."
          echo "Build args:"
          echo "  - DD_GIT_REPOSITORY_URL=${{ github.server_url }}/${{ github.repository }}"
          echo "  - DD_GIT_COMMIT_SHA=${{ github.sha }}"
          echo "  - DISABLE_DDTRACE=false"
          echo "Labels:"
          echo "  - com.datadog.git.repository_url=${{ github.server_url }}/${{ github.repository }}"
          echo "  - com.datadog.git.commit_sha=${{ github.sha }}"

          # Simulate a digest output
          SIMULATED_DIGEST="sha256:$(echo '${{ github.sha }}' | sha256sum | cut -d' ' -f1)"
          echo "digest=${SIMULATED_DIGEST}" >> $GITHUB_OUTPUT
          echo "✅ Docker image built and pushed successfully (simulated)"
          echo "📦 Image digest: ${SIMULATED_DIGEST}"

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

      # SIMULATED: AWS Lambda deployment
      - name: Configure AWS credentials (Simulated)
        run: |
          echo "☁️ Simulating AWS credentials configuration..."
          echo "Region: us-east-1"
          echo "✅ AWS credentials configured (simulated)"

      - name: Invoke Lambda function (Simulated)
        id: invoke_lambda_function
        run: |
          echo "🚀 Simulating Lambda function invocation..."
          echo "Function: nxt-deploy_nomad_job-lambda"
          echo "Payload: {\"job\": \"${{ steps.module-id.outputs.module-id }}\", \"hash\": \"${{ steps.get_sha.outputs.sha7 }}\"}"

          # Create simulated response
          SIMULATED_EVAL_ID="eval-$(date +%s)-$(echo $RANDOM | cut -c1-4)"
          SIMULATED_STATUS_CODE="200"
          SIMULATED_RESPONSE="{\"statusCode\": 200, \"body\": {\"EvalID\": \"${SIMULATED_EVAL_ID}\", \"Warnings\": null}}"

          echo "✅ Lambda function invoked successfully (simulated)"

          # Create a summary of the lambda invocation
          echo "## Lambda Invocation Summary (Simulated)" >> $GITHUB_STEP_SUMMARY
          echo "### Function Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Function:** nxt-deploy_nomad_job-lambda" >> $GITHUB_STEP_SUMMARY
          echo "- **Job Name:** ${{ steps.module-id.outputs.module-id }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit:** ${{ steps.get_sha.outputs.sha7 }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Region:** us-east-1" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Response Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Status Code:** ${SIMULATED_STATUS_CODE}" >> $GITHUB_STEP_SUMMARY
          echo "- **Evaluation ID:** ${SIMULATED_EVAL_ID}" >> $GITHUB_STEP_SUMMARY
          echo "- **Warnings:** None" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "<details><summary><strong>Simulated Response</strong></summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          echo "${SIMULATED_RESPONSE}" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY

          # Set the output for other steps to use if needed
          echo "response=${SIMULATED_RESPONSE}" >> $GITHUB_OUTPUT
          echo "eval_id=${SIMULATED_EVAL_ID}" >> $GITHUB_OUTPUT
          echo "status_code=${SIMULATED_STATUS_CODE}" >> $GITHUB_OUTPUT

      - name: Log output to console
        run: |
          echo "Status Code: ${{ steps.invoke_lambda_function.outputs.status_code }}"
          echo "Evaluation ID: ${{ steps.invoke_lambda_function.outputs.eval_id }}"
          echo "🎉 Deployment workflow completed successfully (simulated)"

name: "🤖 | Process PR"

on:
  pull_request:
    types: [opened, reopened, closed, edited, unlabeled]

jobs:
  process-pr:
    runs-on: ubuntu-latest
    steps:
      - name: Check organization and trigger workflow
        if: ${{ github.repository_owner == 'sensehawk' }}
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            // This approach uses the GitHub API to trigger the external workflow
            // only when running in a sensehawk repository
            const response = await github.rest.repos.createDispatchEvent({
              owner: 'sensehawk',
              repo: 'workflows',
              event_type: 'process-pr',
              client_payload: {
                repository: context.repo.owner + '/' + context.repo.repo,
                pull_request: context.payload.pull_request,
                action: context.payload.action
              }
            });
            console.log('Triggered external workflow successfully');

      - name: Skip for non-sensehawk repositories
        if: ${{ github.repository_owner != 'sensehawk' }}
        run: |
          echo "ℹ️ Skipping PR processing - this workflow only runs for sensehawk repositories"
          echo "Repository owner: ${{ github.repository_owner }}"
